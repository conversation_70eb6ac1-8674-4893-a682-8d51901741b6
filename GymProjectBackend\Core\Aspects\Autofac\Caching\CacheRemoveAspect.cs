using Castle.DynamicProxy;
using Core.Cache.Abstract;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Cache Remove Aspect - Belirtilen pattern'lere göre cache'leri temizler
    /// Multi-tenant yapıya uygun cache invalidation
    /// </summary>
    public class CacheRemoveAspect : MethodInterception
    {
        private string _pattern;
        private IRedisService _redisService;
        private ICacheKeyGenerator _keyGenerator;

        public CacheRemoveAspect(string pattern)
        {
            _pattern = pattern;
            _redisService = ServiceTool.ServiceProvider.GetService<IRedisService>();
            _keyGenerator = ServiceTool.ServiceProvider.GetService<ICacheKeyGenerator>();
        }

        public override void Intercept(IInvocation invocation)
        {
            // Method'u çalıştır
            invocation.Proceed();

            // Başarılı işlem sonrası cache'i temizle - FIRE AND FORGET
            if (invocation.ReturnValue != null)
            {
                try
                {
                    var companyId = GetCompanyIdFromContext();

                    // Company-specific pattern oluştur
                    var companyPattern = _keyGenerator.GenerateCompanyPattern(companyId, $"*{_pattern}*");

                    // Pattern'e uyan cache'leri temizle - Background'da çalıştır
                    Task.Run(async () =>
                    {
                        try
                        {
                            await _redisService.DeleteByPatternAsync(companyPattern);
                        }
                        catch (Exception)
                        {
                            // Cache silme hatası - sessizce yok say
                            // Logging yapılabilir ama blocking olmamalı
                        }
                    });
                }
                catch (Exception)
                {
                    // Cache invalidation hatası - yok say
                }
            }
        }

        private int GetCompanyIdFromContext()
        {
            try
            {
                // HttpContext'ten company ID al
                var httpContextAccessor = ServiceTool.ServiceProvider.GetService<Microsoft.AspNetCore.Http.IHttpContextAccessor>();
                var httpContext = httpContextAccessor?.HttpContext;
                
                if (httpContext?.User?.Identity?.IsAuthenticated == true)
                {
                    var companyIdClaim = httpContext.User.Claims.FirstOrDefault(c => c.Type == "CompanyId");
                    if (companyIdClaim != null && int.TryParse(companyIdClaim.Value, out var companyId))
                    {
                        return companyId;
                    }
                }
                
                // Fallback: Default company ID
                return 1;
            }
            catch
            {
                // Hata durumunda default company ID
                return 1;
            }
        }
    }
}
